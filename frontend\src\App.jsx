import React from 'react'
import { Route, Routes } from 'react-router-dom'
import Home from './pages/Home'
import Userlogin from './pages/Userlogin'
import Usersignup from './pages/Usersignup'
import Captainlogin from './pages/Captainlogin'
import Captainsignup from './pages/Captainsignup'
import Index from './pages/index'
import UserProtectWrapper from './pages/UserProtectWrapper'
import CaptainProtectWrapper from './pages/CaptainProtectWrapper'
import Userlogout from './pages/Userlogout'
import Captainlogout from './pages/Captainlogout'
import CaptainHome from './pages/CaptainHome'
import Riding from './pages/Riding'
// Removed unused import
// import WaitingForCaptain from './components/WaitingForCaptain'

const App = () => {
  return (
    <div>
      <Routes>
        <Route path='/' element={<Home/>} />
        <Route path='/login' element={<Userlogin/>} />
        <Route path='/riding' element={<Riding/>} />
        <Route path='/signup' element={<Usersignup/>} />
        <Route path='/Captain-login' element={<Captainlogin/>} />
        <Route path='/Captain-signup' element={<Captainsignup/>} />
        <Route path='/home' element={
          <UserProtectWrapper>
            <Index/>
          </UserProtectWrapper>
        }/>
        <Route path='/user/logout' element={<Userlogout/>} />

        {/* Captain Routes */}
        <Route path='/captain-home' element={
          <CaptainProtectWrapper>
            <CaptainHome/>
          </CaptainProtectWrapper>
        }/>
        <Route path='/captain/logout' element={<Captainlogout/>} />
      </Routes>
    </div>
  )
}

export default App
