import React, { useState, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import axios from 'axios'
import { CaptainDataContext } from '../Context/Captaincontext'

const Captainsignup = () => {
    const navigate = useNavigate()

    const [email, setEmail] = useState('')
    const [password, setPassword] = useState('')
    const [firstname, setFirstname] = useState('')
    const [lastname, setLastname] = useState('')
    // Vehicle state variables with preferred naming convention
    const [vehicleColor, setVehicleColor] = useState('')
    const [vehiclePlate, setVehiclePlate] = useState('')
    const [vehicleCapacity, setVehicleCapacity] = useState(4)
    const [vehicleType, setVehicleType] = useState('car')
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState('')
    const { setCaptain } = React.useContext(CaptainDataContext)

    // Monitor changes to the data state
    // useEffect(() => {
    //   if (Object.keys(data).length > 0) {
    //     console.log('Data state updated:', data);
    //     // Here you could make an API call to send the data to your backend
    //   }
    // }, [data]);

    // Check if captain is already logged in
    useEffect(() => {
      const checkToken = async () => {
        const captainToken = localStorage.getItem('captainToken');
        if (captainToken) {
          try {
            // Validate token with backend before redirecting
            const baseUrl = import.meta.env.VITE_BASE_URL || 'http://localhost:4000';
            const response = await axios.get(`${baseUrl}/captains/profile`, {
              headers: {
                Authorization: `Bearer ${captainToken}`
              }
            });

            if (response.status === 200) {
              console.log('Captain already logged in, redirecting to captain dashboard');
              navigate('/captain-home');
            }
          } catch (error) {
            console.error('Invalid token, staying on signup page:', error);
            // Clear invalid token
            localStorage.removeItem('captainToken');
            localStorage.removeItem('captainData');
          }
        }
      };

      checkToken();
    }, [navigate]);

    const Handler = async (e) => {
      e.preventDefault();
      setLoading(true);
      setError('');
      try {
        const newCaptain = {
          fullname: {
           firstname: firstname,
           lastname: lastname,
          },
          email,
          password,
          vehicle: {
            color: vehicleColor,
            plate: vehiclePlate,
            capacity: parseInt(vehicleCapacity),
            vehicleType: ['car', 'auto', 'bike'].includes(vehicleType) ? vehicleType : 'car'
          }
        };

        console.log('Sending captain registration data:', JSON.stringify(newCaptain, null, 2));
        const baseUrl = import.meta.env.VITE_BASE_URL || 'http://localhost:4000';
        console.log('API URL:', `${baseUrl}/captains/register`);

        const response = await axios.post(`${baseUrl}/captains/register`, newCaptain);
        console.log('Captain registration response:', response);

        if (response.status >= 200 && response.status < 300) {
          const responseData = response.data;
          console.log('Captain registration successful:', responseData);

          // Log the captain data
          console.log('Captain data:', newCaptain);

          // Get captain data from response
          const captainData = responseData.captain || responseData;
          setCaptain(captainData);

          // Store token in localStorage (using a different key than user token)
          localStorage.setItem('captainToken', responseData.token);

          // Also store captain data in localStorage for persistence
          localStorage.setItem('captainData', JSON.stringify(captainData));

          console.log('Captain token and data stored in localStorage');

          // Navigate to captain dashboard
          navigate('/captain-home');
        }
      } catch (error) {
        console.error('Captain registration error:', error);
        if (error.response) {
          console.error('Error response data:', error.response.data);
          console.error('Error response status:', error.response.status);

          // Handle validation errors
          if (error.response.data.errors && error.response.data.errors.length > 0) {
            const errorMessages = error.response.data.errors.map(err => err.msg).join(', ');
            setError(`Validation failed: ${errorMessages}`);
          } else {
            setError(error.response.data.message || 'Registration failed. Please try again.');
          }
        } else if (error.request) {
          console.error('Error request:', error.request);
          setError('Network error. Please check your connection.');
        } else {
          console.error('Error message:', error.message);
          setError('An unexpected error occurred.');
        }
      } finally {
        setLoading(false);
        setFirstname('');
        setLastname('');
        setEmail('');
        setPassword('');
        setVehicleColor('');
        setVehiclePlate('');
        setVehicleCapacity(4);
        setVehicleType('car');
      }
    }

  return (
    <div className='p-7 flex flex-col justify-between h-screen'>
    <div>
    <img className='w-1/4 mb-5' src="https://static.vecteezy.com/system/resources/previews/027/127/594/large_2x/uber-logo-uber-icon-transparent-free-png.png" alt="" />
      <form onSubmit={(e)=>{Handler(e)}}>
      <h3>What's your name?</h3>
        <div className='flex justify-between gap-4 mb-6 ' >

          <input
          type='text'
          required
          onChange={(e) => setFirstname(e.target.value)}
          value={firstname}
          placeholder='First Name'
          className='border border-gray-300 bg-[#eeeeee] w-1/2 py-2 px-4 rounded mt-2'
          />

        <input
          type='text'
          required
          onChange={(e) => setLastname(e.target.value)}
          value={lastname}
          placeholder='Last Name'
          className='border border-gray-300 bg-[#eeeeee] w-1/2 py-2 px-4 rounded mt-2'
          />
        </div>

        <h3 className='text'>What's your email?</h3>
        <input
        type='email'
        required
        onChange={(e) => setEmail(e.target.value)}
        value={email}
        placeholder='<EMAIL>'
        className='border border-gray-300 bg-[#eeeeee] w-full py-2 px-4 rounded mt-2' />
        <h3 className='mt-3 text'>Password</h3>
        <input
        type='password'
        required
        onChange={(e)=>setPassword(e.target.value)}
        value={password}
        placeholder='**********'
        className='border border-gray-300 bg-[#eeeeee] w-full py-2 px-4 rounded mt-2' />

        <h3 className='mt-5 text-lg font-semibold'>Vehicle Information</h3>
        <div className='bg-gray-50 p-4 rounded-lg border border-gray-200 mt-2'>
          <div className='mb-4'>
            <label className='block text-sm font-medium text-gray-700 mb-1'>Vehicle Color</label>
            <input
              type='text'
              required
              onChange={(e) => setVehicleColor(e.target.value)}
              value={vehicleColor}
              placeholder='e.g., Red, Blue, Black'
              className='border border-gray-300 bg-[#eeeeee] w-full py-2 px-4 rounded'
            />
          </div>

          <div className='mb-4'>
            <label className='block text-sm font-medium text-gray-700 mb-1'>License Plate</label>
            <input
              type='text'
              required
              onChange={(e) => setVehiclePlate(e.target.value)}
              value={vehiclePlate}
              placeholder='e.g., ABC123'
              className='border border-gray-300 bg-[#eeeeee] w-full py-2 px-4 rounded'
            />
          </div>

          <div className='flex justify-between gap-4'>
            <div className='w-1/2'>
              <label className='block text-sm font-medium text-gray-700 mb-1'>Vehicle Type</label>
              <select
                required
                onChange={(e) => setVehicleType(e.target.value)}
                value={vehicleType}
                className='border border-gray-300 bg-[#eeeeee] w-full py-2 px-4 rounded'
              >
                <option value="car">Car</option>
                <option value="auto">Auto</option>
                <option value="bike">Bike</option>
              </select>
            </div>

            <div className='w-1/2'>
              <label className='block text-sm font-medium text-gray-700 mb-1'>Passenger Capacity</label>
              <input
                type='number'
                required
                min="1"
                max="10"
                onChange={(e) => setVehicleCapacity(e.target.value)}
                value={vehicleCapacity}
                placeholder='e.g., 4'
                className='border border-gray-300 bg-[#eeeeee] w-full py-2 px-4 rounded'
              />
            </div>
          </div>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mt-4 mb-4">
            {error}
          </div>
        )}
        <button
          className='flex items-center justify-center w-full bg-black text-white py-3 mt-5 rounded text-lg'
          disabled={loading}
        >
          {loading ? 'Signing Up...' : 'Sign Up'}
        </button>
      </form>
      <div className='mt-2'>
        <p className='text-center'>Already have an account? <Link to={'/Captain-login'} className='text-blue-500'>Login as Captain</Link></p>
      </div>
    </div>
   <p className='mt-5 leading-4 text-sm'>By continuing, you agree to Uber's Terms of Service and acknowledge that you have read our Privacy Policy.
We may use your information for account security, customer support, and to send you updates about our services.</p>
  </div>
  )
}

export default Captainsignup
