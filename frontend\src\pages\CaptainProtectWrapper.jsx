import React, { useContext, useEffect, useState } from "react";
import { CaptainDataContext } from "../Context/Captaincontext";
import { useNavigate } from "react-router-dom";
import axios from "axios";

const CaptainProtectWrapper = ({children}) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  const { setCaptain } = useContext(CaptainDataContext);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Get token from localStorage - use captainToken for captains
        const token = localStorage.getItem('captainToken');
        console.log('Captain token from localStorage:', token);

        if (!token) {
          console.log('No captain token found, redirecting to captain login');
          setIsAuthenticated(false);
          navigate('/Captain-login');
          return;
        }

        try {
          // Always validate token with backend
          const baseUrl = import.meta.env.VITE_BASE_URL || 'http://localhost:4000';
          const response = await axios.get(`${baseUrl}/captains/profile`, {
            headers: {
              Authorization: `Bearer ${token}`
            }
          });

          if (response.status === 200) {
            console.log('Captain profile fetched successfully:', response.data);
            setCaptain(response.data);
            setIsAuthenticated(true);
          } else {
            throw new Error('Failed to validate token');
          }
        } catch (apiError) {
          console.error('API validation error:', apiError);
          // Clear invalid tokens and data
          localStorage.removeItem('captainToken');
          localStorage.removeItem('captainData');
          setIsAuthenticated(false);
          navigate('/Captain-login');
        }
      } catch (error) {
        console.error('Captain authentication error:', error);
        localStorage.removeItem('captainToken');
        localStorage.removeItem('captainData');
        setIsAuthenticated(false);
        navigate('/Captain-login');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [navigate, setCaptain]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <>
      {isAuthenticated ? children : null}
    </>
  );
}

export default CaptainProtectWrapper
