import React, { useState, useRef, useEffect, useContext } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import 'remixicon/fonts/remixicon.css'
import CaptainDetails from '../components/CaptainDetails'
import RidePopup from '../components/RidePopup'
import ConfirmRidePopup from '../components/ConfirmRidePopup'
import gsap from 'gsap'
import { useGSAP } from '@gsap/react'
import { CaptainDataContext } from '../Context/Captaincontext'
import FinishRide from '../components/FinishRide'

const CaptainRiding = (props) => {

  const [finishRidepanel, setfinishRidepanel] = useState(false);
  const finishRidepanelRef = useRef(null)

   useGSAP(function(){
    if(finishRidepanel){
      gsap.to(finishRidepanelRef.current,{
        translateY:'0%'
      })
    }
    else{
      gsap.to(finishRidepanelRef.current,{
     translateY:'100%'
      })
    }
  },[finishRidepanel])


  return (
    <div className='h-screen relative'>
       <div className='flex justify-between items-center fixed p-3 top-0 w-full '>
        <img className='w-16 left-6 top-7 absolute' src='https://1000logos.net/wp-content/uploads/2021/04/Uber-logo.png' alt='' />
        <div>
          <Link className='h-10 w-10 pl-80 ml-3 pt-3 bg right-2 bg flex items-centre justify-center' to={'/captain/logout'}><i className="text-xl font-bold flex items-centre justify-center ri-logout-circle-r-line"></i></Link>
        </div>
      </div>

      <div className='h-4/5'>
        <img className='h-full w-full object-cover' src="https://storage.googleapis.com/support-forums-api/attachment/thread-190038693-17286932122251387759.png" alt="" />
      </div>
     <div className='h-1/5 p-6 flex relative pt-10 bg-yellow-300 justify-between items-center' 
     
     onClick={()=>{
      setfinishRidepanel(true)
     }}
     >
      <h5 className='p-1  w-[93%] top-0 absolute rounded-full' >
      <i className="ri-arrow-up-wide-fill pl-36 text-3xl  text-gray-800"></i>
     </h5>
     <h4 className='text-xl font-semibold'>4km away</h4>
     <button className='bg-green-500 font-semibold text-white rounded-lg p-3 px-10'>Complete Ride</button>
     </div>
       <div  ref={finishRidepanelRef} className='bottom-0 h-screen translate-y-full  fixed z-10 bg-white w-full  px-3 py-6 pt-12'>
        <FinishRide setfinishRidepanel={setfinishRidepanel} />
      </div>
    </div>
  )
}

export default CaptainRiding
