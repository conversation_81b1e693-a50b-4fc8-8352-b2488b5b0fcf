import React from 'react'
import 'remixicon/fonts/remixicon.css'

const WaitingForCaptain = (props) => {
  return (
    <div>
    <h5 onClick={()=>{
      props.setwaitingForCaptainPanel(false)
     }} className='text-xl color-red absolute right-5 top-5 cusor-pointer '><i className="ri-close-fill bg-red-500"></i></h5>
     <h3 className='text-lg font-medium mb-3'>Waiting For Captain</h3> <hr className=''></hr>
      <div className='flex justify-between items-center'>
         <img  className='h-14 pr-2 pl-5 pt-5' src="https://logodix.com/logo/640751.png" alt="" />
         <div className='mt-3 text-right'>
          <h2 className='text-lg font-medium'>John <PERSON></h2>
          <h3 className='text-xl font-bold -mt-2 -mb-1'>TG02A4823</h3>
          <p className='text-sm font-medium text-gray-600'>Swift Desire 2018</p>
         </div>
      </div>
     <div className='flex items-center gap-2 flex-col justify-between mt-3' >
      
         <div className=' flex flex-col justify-between w-full'>
             <div className='flex border-green-500 p-3 w-full'>
                 <h5 className='pt-4'><i className="ri-map-pin-range-fill"></i></h5>
                <div className='flex flex-col ml-6'>
                <h1 className='font-semibold text-xl' >112/11-A</h1>
                <p className='font-normal text-sm text-gray-500 '>Pushp Vihar, Bangalore, India</p>
                </div>
             </div><hr className=''></hr>
             <div className='flex mt-2 border-green-500 p-3 w-full'>
             <h5 className='pt-4'><i className="ri-checkbox-blank-fill"></i></h5>
                <div className='flex flex-col ml-6'>
                <h1 className='font-semibold text-xl' >Gandhi Bhavan</h1>
                <p className='font-normal text-sm text-gray-500 '>17th Cross Road PWD Quartres 1<sup>st</sup> Sector Bangalore, India</p>
                </div>
             </div><hr></hr>
             <div className='flex mt-2 border-green-500 p-3 w-full'>
             <h5 className='pt-2'><i className="ri-wallet-fill"></i></h5>
                <div className='flex flex-col ml-6'>
                <h1 className='font-semibold text-xl' >$192.20</h1>
              
                </div>
             </div>
         </div>
        
     </div>
 </div>
  )
}

export default WaitingForCaptain
