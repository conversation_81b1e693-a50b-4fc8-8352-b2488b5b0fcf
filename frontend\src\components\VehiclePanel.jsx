import React from 'react'
import 'remixicon/fonts/remixicon.css'

const VehiclePanel = (props) => {
  return (
    <div>
          <h5 onClick={()=>{
          props.setVehiclePanel(false)
          props.setPanelOpen(true)
        }} className='text-xl color-red absolute right-5 top-5 cusor-pointer '><i className="ri-close-fill bg-red-500"></i></h5>
        <h3 className='text-lg font-medium mb-3'>Select a vehicle</h3>
        <div onClick={()=>{props.setConfirmRidepanel(true)}} className='flex mb-2 border-2  hover:border-black rounded-xl w-full items-center justify-between p-3'>
          <img className='h-14 pr-2' src="https://i.pinimg.com/originals/93/c1/05/93c105244c0a3de81267a89cb13386f7.png" alt="" />
          <div className='w-1/2' >
            <h4 className='font-medium text-sm'>Uber Go <span>4 <i className="ri-user-fill"></i></span></h4>
            <h5 className='font-medium text-sm'>2 min Away</h5>
            <p className='text-xs text-gray-600 font-normal'>Affortable Comfort</p>
          </div>
          <h2 className='text-lg font-medium'>$192.20</h2>
        </div>
        <div onClick={()=>{props.setConfirmRidepanel(true)}} className='flex border-2 mb-2  hover:border-black rounded-xl w-full items-center justify-between p-3'>
          <img className='h-14 pr-2' src="https://www.uber-assets.com/image/upload/f_auto,q_auto:eco,c_fill,w_956,h_637/v1649231091/assets/2c/7fa194-c954-49b2-9c6d-a3b8601370f5/original/Uber_Moto_Orange_312x208_pixels_Mobile.png" alt="" />
          <div className='w-1/2' >
            <h4 className='font-medium text-sm'>Uber Bike <span>1 <i className="ri-user-fill"></i></span></h4>
            <h5 className='font-medium text-sm'>1 min Away</h5>
            <p className='text-xs text-gray-600 font-normal'>Comfort,Easy travel</p>
          </div>
          <h2 className='text-lg font-medium'>$90</h2>
        </div>
        <div onClick={()=>{props.setConfirmRidepanel(true)}} className='flex border-2 mb-2  hover:border-black rounded-xl w-full items-center justify-between p-3'>
          <img className='h-14 pr-2' src="https://www.uber-assets.com/image/upload/f_auto,q_auto:eco,c_fill,h_368,w_552/v1648431773/assets/1d/db8c56-0204-4ce4-81ce-56a11a07fe98/original/Uber_Auto_558x372_pixels_Desktop.png" alt="" />
          <div className='w-1/2' >
            <h4 className='font-medium text-sm'>Uber Auto <span>3 <i className="ri-user-fill"></i></span></h4>
            <h5 className='font-medium text-sm'>1 min Away</h5>
            <p className='text-xs text-gray-600 font-normal'>Comfort,Easy travel</p>
          </div>
          <h2 className='text-lg font-medium'>$120</h2>
        </div>
    </div>
  )
}

export default VehiclePanel
