import React from 'react';

const AuthCheckpoint = ({ type, isAuthenticated }) => {
  return (
    <div className="fixed top-0 left-0 right-0 bg-gray-800 text-white p-2 text-center">
      {isAuthenticated ? (
        <div className="flex items-center justify-center">
          <div className="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
          <span>{type} authenticated successfully</span>
        </div>
      ) : (
        <div className="flex items-center justify-center">
          <div className="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
          <span>{type} not authenticated</span>
        </div>
      )}
    </div>
  );
};

export default AuthCheckpoint;
