import React, { useState, useEffect, useContext } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import axios from 'axios'
import { CaptainDataContext } from '../Context/Captaincontext'

const Captainlogin = () => {

  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const { setCaptain } = useContext(CaptainDataContext)
  const navigate = useNavigate()

  // Check if captain is already logged in
  useEffect(() => {
    const checkToken = async () => {
      const captainToken = localStorage.getItem('captainToken');
      if (captainToken) {
        try {
          // Validate token with backend before redirecting
          const baseUrl = import.meta.env.VITE_BASE_URL || 'http://localhost:4000';
          const response = await axios.get(`${baseUrl}/captains/profile`, {
            headers: {
              Authorization: `Bearer ${captainToken}`
            }
          });

          if (response.status === 200) {
            console.log('Captain already logged in, redirecting to captain dashboard');
            navigate('/captain-home');
          }
        } catch (error) {
          console.error('Invalid token, staying on login page:', error);
          // Clear invalid token
          localStorage.removeItem('captainToken');
          localStorage.removeItem('captainData');
        }
      }
    };

    checkToken();
  }, [navigate]);

  const Handler = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const loginData = {
        email,
        password,
      };

      console.log('Sending captain login data:', loginData);
      const baseUrl = import.meta.env.VITE_BASE_URL || 'http://localhost:4000';
      const response = await axios.post(`${baseUrl}/captains/login`, loginData);
      console.log('Captain login response:', response);

      if (response.status >= 200 && response.status < 300) {
        const responseData = response.data;
        console.log('Captain login successful:', responseData);

        // Get captain data from response
        const captainData = responseData.captain || responseData;
        setCaptain(captainData);

        // Store token in localStorage (using a different key than user token)
        localStorage.setItem('captainToken', responseData.token);

        // Also store captain data in localStorage for persistence
        localStorage.setItem('captainData', JSON.stringify(captainData));

        console.log('Captain token and data stored in localStorage');

        // Navigate to captain dashboard
        navigate('/captain-home');
      }
    } catch (error) {
      console.error('Captain login error:', error);
      if (error.response) {
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
        setError(error.response.data.message || 'Login failed. Please check your credentials.');
      } else if (error.request) {
        console.error('Error request:', error.request);
        setError('Network error. Please check your connection.');
      } else {
        console.error('Error message:', error.message);
        setError('An unexpected error occurred.');
      }
    } finally {
      setLoading(false);
      setEmail('');
      setPassword('');
    }
  }


  return (
    <div className='p-7 flex flex-col justify-between h-screen'>
    <div>
    <img className='w-1/4 mb-5' src="https://static.vecteezy.com/system/resources/previews/027/127/594/large_2x/uber-logo-uber-icon-transparent-free-png.png" alt="" />
      <form onSubmit={(e)=>{Handler(e)}}>
        <h3 className='text-lg'>What's your email?</h3>
        <input
        type='email'
        required
        onChange={(e) => setEmail(e.target.value)}
        value={email}
        placeholder='<EMAIL>'
        className='border border-gray-300 bg-[#eeeeee] w-full py-2 px-4 rounded mt-2' />
        <h3 className='mt-2 text-lg'>Password</h3>
        <input
        type='password'
        required
        onChange={(e)=>setPassword(e.target.value)}
        value={password}
        placeholder='**********'
        className='border border-gray-300 bg-[#eeeeee] w-full py-2 px-4 rounded mt-2' />
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mt-4 mb-4">
            {error}
          </div>
        )}
        <button
          className='flex items-center justify-center w-full bg-black text-white py-3 mt-6 rounded text-lg'
          disabled={loading}
        >
          {loading ? 'Logging in...' : 'Login'}
        </button>
      </form>
      <div className='mt-2'>
        <p className='text-center'>Don't have an account? <Link to={'/Captain-signup'} className='text-blue-500'>Register As a Captain</Link></p>
      </div>
    </div>
    <Link to={'/login'} className='flex items-center justify-center w-full bg-[#CD9575] text-white py-3 mt-12 rounded text-lg mb-5 '>Sign In as User</Link>
  </div>
  )
}

export default Captainlogin
