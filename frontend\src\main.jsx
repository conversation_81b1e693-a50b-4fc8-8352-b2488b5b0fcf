import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.jsx'
import { BrowserRouter } from 'react-router-dom'
import Usercontext from './Context/Usercontext'
import Captaincontext from './Context/Captaincontext'

createRoot(document.getElementById('root')).render(
  <StrictMode>
    <Usercontext>
      <Captaincontext>
        <BrowserRouter>
          <App />
        </BrowserRouter>
      </Captaincontext>
    </Usercontext>
  </StrictMode>,
)
