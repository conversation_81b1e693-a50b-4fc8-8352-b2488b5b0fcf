# Uber Backend API Documentation

This document provides information about the backend API endpoints, their usage, and the structure of the codebase.

## Table of Contents

- [API Endpoints](#api-endpoints)
  - [User Registration](#user-registration)
  - [User Profile](#user-profile)
- [File Structure](#file-structure)
  - [User Controller](#user-controller)
  - [User Model](#user-model)
  - [User Service](#user-service)
  - [User Routes](#user-routes)
- [Environment Variables](#environment-variables)
- [Getting Started](#getting-started)
- [Using the Authentication Token](#using-the-authentication-token)

## API Endpoints

### User Registration

Register a new user in the system.

**URL**: `/users/register`

**Method**: `POST`

**Authentication**: None

**Request Body Formats**:

The API supports two formats for the request body:

1. **Nested Structure**:
```json
{
  "fullname": {
    "firstname": "<PERSON>",
    "lastname": "<PERSON><PERSON>"
  },
  "email": "<EMAIL>",
  "password": "password123"
}
```

2. **Flat Structure**:
```json
{
  "firstname": "<PERSON>",
  "lastname": "<PERSON><PERSON>",
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Validation Rules**:
- `email`: Must be a valid email format
- `password`: Must be at least 6 characters long
- `firstname`/`fullname.firstname`: Must be at least 3 characters long

**Success Response**:
- **Code**: 201 Created
- **Content Example**:
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2N2ZmYjkzOWQ4ODgyZmFjOWExYTFkZjgiLCJpYXQiOjE3NDQ4MTIzNDUsImV4cCI6MTc0NDg5ODc0NX0.rDgIvcsKHXLgQOjAcunTa6E85RiCbdqCB0j6AE_MnN4",
  "user": {
    "fullname": {
      "firstname": "John",
      "lastname": "Doe"
    },
    "email": "<EMAIL>",
    "password": "$2b$10$AeufTx.rJ.pOvkzQrjE/.OIbNguHFMpwQRIHKkKwdkdCETqOjlV.a",
    "_id": "67ffb939d8882fac9a1a1df8",
    "__v": 0
  }
}
```

**Response Details**:

1. **token**: A JWT (JSON Web Token) that should be included in subsequent authenticated requests. The token contains:
   - User ID (encoded in the payload)
   - Issue time (iat)
   - Expiration time (exp) - set to 24 hours after creation

2. **user**: The newly created user object with the following properties:
   - **fullname**: Object containing the user's first and last name
   - **email**: The user's email address
   - **password**: The hashed password (not the original password)
   - **_id**: MongoDB unique identifier for the user
   - **__v**: MongoDB version key

**Error Responses**:

1. **Email Already Exists**:
   - **Code**: 400 Bad Request
   - **Content**:
   ```json
   {
     "message": "User already exist"
   }
   ```
   - **Description**: This error occurs when attempting to register with an email that is already in the database.

2. **Validation Error - First Name Too Short**:
   - **Code**: 400 Bad Request
   - **Content Example**:
   ```json
   {
     "errors": [
       {
         "type": "field",
         "value": "Jo",
         "msg": "First name must be at least 3 characters",
         "path": "firstname",
         "location": "body"
       }
     ]
   }
   ```
   - **Description**: This error occurs when the first name provided is less than 3 characters long.

3. **Validation Error - Invalid Email Format**:
   - **Code**: 400 Bad Request
   - **Content Example**:
   ```json
   {
     "errors": [
       {
         "type": "field",
         "value": "invalid-email",
         "msg": "Invalid email",
         "path": "email",
         "location": "body"
       }
     ]
   }
   ```
   - **Description**: This error occurs when the email provided is not in a valid format.

4. **Validation Error - Password Too Short**:
   - **Code**: 400 Bad Request
   - **Content Example**:
   ```json
   {
     "errors": [
       {
         "type": "field",
         "value": "12345",
         "msg": "Password must be at least 6 characters",
         "path": "password",
         "location": "body"
       }
     ]
   }
   ```
   - **Description**: This error occurs when the password provided is less than 6 characters long.

### User Profile

Retrieve the profile information of the authenticated user.

**URL**: `/users/profile`

**Method**: `GET`

**Authentication**: Required (JWT Token)

**Request Headers**:
```
Authorization: Bearer <token>
```

**Success Response**:
- **Code**: 200 OK
- **Content Example**:
```json
{
  "fullname": {
    "firstname": "John",
    "lastname": "Doe"
  },
  "email": "<EMAIL>",
  "_id": "67ffb939d8882fac9a1a1df8",
  "__v": 0
}
```

**Error Responses**:

1. **No Token Provided**:
   - **Code**: 401 Unauthorized
   - **Content**:
   ```json
   {
     "message": "Unauthorized - No token provided"
   }
   ```
   - **Description**: This error occurs when no authentication token is provided.

2. **Invalid Token**:
   - **Code**: 401 Unauthorized
   - **Content**:
   ```json
   {
     "message": "Invalid token"
   }
   ```
   - **Description**: This error occurs when the provided token is not valid.

3. **Token Expired**:
   - **Code**: 401 Unauthorized
   - **Content**:
   ```json
   {
     "message": "Token expired"
   }
   ```
   - **Description**: This error occurs when the provided token has expired.

4. **User Not Found**:
   - **Code**: 401 Unauthorized
   - **Content**:
   ```json
   {
     "message": "User not found - Token may be for a deleted user"
   }
   ```
   - **Description**: This error occurs when the user associated with the token no longer exists.

## File Structure

### User Controller

**File**: `controllers/user.controller.js`

The controller handles HTTP requests and responses for user-related operations. It validates input, processes the request, and returns appropriate responses.

**Key Functions**:
- `registerUser`: Handles user registration
- `loginUser`: Handles user login
- `getUserProfile`: Retrieves user profile information
- `logoutUser`: Handles user logout

### User Model

**File**: `models/user.model.js`

The model defines the database schema for user data and provides methods for password hashing, comparison, and JWT token generation.

**Schema**:
- `fullname`: Object containing firstname and lastname
  - `firstname`: String, required, min length 3
  - `lastname`: String, min length 3
- `email`: String, required, unique, min length 5
- `password`: String, required, not returned in queries
- `socketId`: String, optional

**Methods**:
- `generateAuthToken`: Generates a JWT token for authentication
- `comparePassword`: Compares a plain text password with the hashed password
- `hashPassword`: Hashes a password using bcrypt

### User Service

**File**: `services/user.service.js`

The service layer contains business logic for user operations, separating it from the controller.

**Key Functions**:
- `createUser`: Creates a new user in the database

### User Routes

**File**: `routes/user.routes.js`

Defines the routes for user-related endpoints and their validation rules.

**Routes**:
- `POST /register`: Register a new user
  - Validates email format
  - Validates password length (min 6 characters)
  - Validates firstname length (min 3 characters)

## Environment Variables

The application uses the following environment variables:
- `PORT`: The port on which the server runs (default: 4000)
- `DB_URL`: MongoDB connection string
- `JWT_SECRET`: Secret key for JWT token generation

## Getting Started

1. Install dependencies:
   ```
   npm install
   ```

2. Create a `.env` file with the required environment variables.

3. Start the server:
   ```
   node server.js
   ```

4. The API will be available at `http://localhost:4000`

## Using the Authentication Token

After successful registration or login, you will receive a JWT token. This token should be included in the Authorization header for subsequent authenticated requests:

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2N2ZmYjkzOWQ4ODgyZmFjOWExYTFkZjgiLCJpYXQiOjE3NDQ4MTIzNDUsImV4cCI6MTc0NDg5ODc0NX0.rDgIvcsKHXLgQOjAcunTa6E85RiCbdqCB0j6AE_MnN4
```

### Example Using cURL

```bash
curl -X GET \
  http://localhost:4000/users/profile \
  -H 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJfaWQiOiI2N2ZmYjkzOWQ4ODgyZmFjOWExYTFkZjgiLCJpYXQiOjE3NDQ4MTIzNDUsImV4cCI6MTc0NDg5ODc0NX0.rDgIvcsKHXLgQOjAcunTa6E85RiCbdqCB0j6AE_MnN4'
```

### Example Using Postman

1. Set the request method to GET and enter the URL (e.g., `http://localhost:4000/users/profile`)
2. Go to the "Authorization" tab
3. Select "Bearer Token" from the Type dropdown
4. Paste your token in the "Token" field
5. Click "Send" to make the request
