
const mongoose = require('mongoose');


const blacklistTokenSchema = new mongoose.Schema({
    token: {
        type: String,
        required: true,
        unique: true
    },
    createdAt: {
        type: Date,
        default: Date.now,
        expires: '846400' // TTL index - document will be automatically deleted after 24 hours
    }
});

const BlacklistToken = mongoose.model('blacklist_token', blacklistTokenSchema);

module.exports = BlacklistToken;