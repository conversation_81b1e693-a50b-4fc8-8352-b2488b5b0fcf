{"name": "uber", "version": "1.0.0", "main": "nodemon server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "nodemon server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcrypt": "^5.1.1", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "express-validator": "^7.2.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.13.2", "nodemon": "^3.1.9"}}