import React, { useState, useRef } from 'react'
import { Link } from 'react-router-dom'
import 'remixicon/fonts/remixicon.css'
import axios from 'axios';
import CaptainDetails from '../components/CaptainDetails'
import RidePopup from '../components/RidePopup';
import gsap from 'gsap';
import { useGSAP } from '@gsap/react';
import ConfirmRidePopup from '../components/ConfirmRidePopup';

const CaptainDashboard = () => {

  const [RidePopuppanel, setRidePopuppanel] = useState(true);
  const [ConfirmRidePopuppanel, setConfirmRidePopuppanel] = useState(false);
  const ConfirmRidePopuppanelRef = useRef(null)

  const RidePopuppanelRef = useRef(null)
  useGSAP(function(){
    if(RidePopuppanel){
      gsap.to(RidePopuppanelRef.current,{
        translateY:'0%'
      })
    }
    else{
      gsap.to(RidePopuppanelRef.current,{
        translateY:'100%'
      })
    }
  },[RidePopuppanel])

  useGSAP(function(){
    if(ConfirmRidePopuppanel){
      gsap.to(ConfirmRidePopuppanelRef.current,{
        translateY:'0%'
      })
    }
    else{
      gsap.to(ConfirmRidePopuppanelRef.current,{
     translateY:'100%'
      })
    }
  },[ConfirmRidePopuppanel])

  return (
    <div className='h-screen'>

      <div className='flex justify-between   items-center fixed p-3 top-0 w-full '>
        <img className='w-16 left-6 top-7 absolute' src='https://1000logos.net/wp-content/uploads/2021/04/Uber-logo.png' alt='' />
        <div>
          <Link className=' h-10 w-10 pl-80 ml-3 pt-3 bg right-2 bg flex items-centre justify-center' to={'/captain/logout'}><i className=" text-xl font-bold flex items-centre justify-center ri-logout-circle-r-line"></i></Link>
        </div>
      </div>

      <div className='h-3/5'>
        <img className='h-full w-full object-cover' src="https://storage.googleapis.com/support-forums-api/attachment/thread-190038693-17286932122251387759.png" alt="" />
      </div>
      <CaptainDetails/>
      <div  ref={RidePopuppanelRef} className='bottom-0 translate-y-full  fixed z-10 bg-white w-full  px-3 py-6 pt-12'>
        <RidePopup  setRidePopuppanel={setRidePopuppanel} setConfirmRidePopuppanel={setConfirmRidePopuppanel}/>
      </div>
      <div  ref={ConfirmRidePopuppanelRef} className='bottom-0 h-screen translate-y-full  fixed z-10 bg-white w-full  px-3 py-6 pt-12'>
        <ConfirmRidePopup  setConfirmRidePopuppanel={setConfirmRidePopuppanel} setRidePopuppanel={setRidePopuppanel}/>
      </div>
    </div>
  )
}

export default CaptainDashboard
