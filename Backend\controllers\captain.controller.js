const { validationResult } = require('express-validator');
const captainModel = require('../models/captain.model');
const captainService = require('../services/captain.service');
const BlacklistTokenModel = require('../models/blacklistToken.model');


module.exports.registerCaptain=async(req,res,next)=>{
const errors=validationResult(req);

if(!errors.isEmpty()){
    return res.status(400).json({errors:errors.array()});
}

const { fullname, firstname, lastname, email, password, vehicle } = req.body;

const isCaptainAlready = await captainModel.findOne({ email });

if (isCaptainAlready) {
    return res.status(400).json({ message: 'Captain already exist' });
}

const hashedPassword = await captainModel.hashPassword(password);

// Handle both nested and flat structure
let userFirstname = firstname;
let userLastname = lastname;

// If fullname object is provided, use its values
if (fullname && fullname.firstname) {
    userFirstname = fullname.firstname;
}
if (fullname && fullname.lastname) {
    userLastname = fullname.lastname;
}

const captain = await captainService.createCaptain({
    firstname: userFirstname,
    lastname: userLastname,
    email,
    password: hashedPassword,
    vechileColor: vehicle.color,
    vechilePlate: vehicle.plate,
    vechileCapacity: vehicle.capacity,
    vechileType: vehicle.vehicleType
});

const token = captain.generateAuthToken();

res.status(201).json({ token, captain });
}

module.exports.loginCaptain=async(req,res,next)=>{
    const errors=validationResult(req);

    if(!errors.isEmpty()){
        return res.status(400).json({errors:errors.array()});
    }

    const {email,password} = req.body;

    const captain = await captainModel.findOne({email}).select('+password');

    if(!captain)
{
    return res.status(401).json({message:'Invalid email or password'});
}

const isMatch = await captain.comparePassword(password);

if(!isMatch){
    return res.status(401).json({message:'Invalid email or password'});
}

    const token=captain.generateAuthToken();

    res.cookie('token',token);

    res.status(200).json({token,captain});

}

module.exports.getCaptainProfile=async(req,res,next)=>{
    res.status(200).json(req.captain);
}

module.exports.logoutCaptain=async(req,res,next)=>{
    // Get token from cookies or authorization header
    const token = req.cookies.token || req.headers.authorization?.split(' ')[1];

    // Clear the cookie
    res.clearCookie('token');

    // Only blacklist if token exists
    if (token) {
        await BlacklistTokenModel.create({ token });
    }

    res.status(200).json({ message: 'Logged out' });
}
