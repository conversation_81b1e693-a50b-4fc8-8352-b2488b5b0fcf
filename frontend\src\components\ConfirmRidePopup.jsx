import React, { useState } from 'react'
import 'remixicon/fonts/remixicon.css'
import { Link } from 'react-router-dom'

const ConfirmRidePopup = (props) => {

      const [otp,setOtp] = useState("")

      const sumbitHandler= (e)=>{
         e.preventDefault()
      }

  return (
    <div>
            <h5 onClick={()=>{
         props.setConfirmRidePopuppanel(false)
         props.setRidePopuppanel(false)
        }} className='text-xl color-red absolute right-5 top-5 cusor-pointer '><i className="ri-close-fill bg-red-500"></i></h5>
        <h3 className='text-lg font-medium mb-3'>Confirm This Ride</h3> <hr className=''></hr>
        <div className='flex items-center justify-between   rounded-lg'>
        <div className='flex  items-center gap-2' >
            <img className='h-12 w-12 object-cover ' src="https://www.mockofun.com/wp-content/uploads/2019/12/circle-profile-pic-768x730.jpg" alt="" />
            <h4 className='text-gray-600'>Alex Carry</h4>
          </div>
         <div className='flex flex-col items-center justify-center pt-4'>
         <h4 className='text-lg font-medium'>₹200.25</h4>
         <p className='text-sm text-gray-600'>10.5 KM</p>
         </div>
        </div>
        <div className='flex items-center flex-col justify-between mt-3' >
           
            <div className=' flex flex-col justify-between w-full'>
                <div className='flex border-green-500 pb-3 w-full'>
                 
                   <div className='flex flex-col ml-3'>
                   <h3 className='font-normal text-lg' >Pick Up</h3>
                   <p className='font-normal text-sm text-gray-500 '>Pushp Vihar, Bangalore, India</p>
                   </div>
                </div><hr className=''></hr>
                <div className='flex mt-2 border-green-500 pb-3 w-full'>
              
                   <div className='flex flex-col ml-3'>
                   <h3 className='font-normal text-lg' >Drop Off</h3>
                   <p className='font-normal text-sm text-gray-500 '>17th Cross Road PWD Quartres 1<sup>st</sup> Sector Bangalore, India</p>
                   </div>
                   
                </div><hr></hr>
                <div className='flex mt-2 border-green-500 pb-3 w-full'>
                
                   <div className='flex flex-col ml-3'>
                   <h3 className='font-normal text-lg' >Note</h3>
                   <p className='font-normal text-sm text-gray-500 '>Lorem ipsum dolor sit amet consectetur adipisicing elit. Repellendus quia, ?</p>
                   </div>
                   
                </div><hr></hr>
                <div className='flex mt-2 border-green-500 gap-6 pb-3 w-full'>
        
                   <div className='flex flex-col ml-3'>
                   <h3 className='text-gray-500 text-xs' >Tip Fare</h3>
                <div className='flex w-screen justify-between'>
                <p className='font-normal text-sm text-black-500'>Amount </p>
                <p className='font-normal text-sm text-black-500 pr-10'>₹100</p>
                </div>
                <div className='flex w-screen justify-between'>
                <p className='font-normal text-sm text-black-500 '>Discount</p>
                <p className='font-normal text-sm text-black-500 pr-10'>₹100.25</p>
                </div>
                <div className='flex w-screen justify-between'>
                <p className='font-normal text-sm text-black-500 '>Total</p>
                <p className='font-normal text-sm text-black-500 pr-10'>₹200.25</p>
                </div>
                   </div>
                   
                </div><hr></hr>
            
            </div>
           <div className='mt-6 w-full'>
            <form onSubmit={(e)=>{
                sumbitHandler(e)
            }}>
               <input value={otp} onChange={(e)=>{
                 setOtp(e.target.value)
               }} type='number' placeholder='Enter OTP' className='border border-gray-300 font-mono text-center bg-[#eeeeee] w-full py-2 px-6 rounded-lg' />
                <Link to={'/captain-riding'} className='bg-green-600 flex justify-center  text-white w-full py-2 rounded-lg mt-3'>Confirm</Link>
            </form>
           </div>
        </div>
    </div>
  )
}

export default ConfirmRidePopup
