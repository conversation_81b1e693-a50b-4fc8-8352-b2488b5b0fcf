
import React from 'react'
import 'remixicon/fonts/remixicon.css'

const RidePopup = (props) => {
  return (
    <div>
            <h5 onClick={()=>{
         props.setRidePopuppanel(false)
        }} className='text-xl color-red absolute right-5 top-5 cusor-pointer '><i className="ri-close-fill bg-red-500"></i></h5>
        <h3 className='text-lg font-medium mb-3'>Ride Available</h3> <hr className=''></hr>
        <div className='flex items-center justify-between   rounded-lg'>
        <div className='flex  items-center gap-2' >
            <img className='h-12 w-12 object-cover ' src="https://www.mockofun.com/wp-content/uploads/2019/12/circle-profile-pic-768x730.jpg" alt="" />
            <h4 className='text-gray-600'><PERSON></h4>
          </div>
         <div className='flex flex-col items-center justify-center pt-4'>
         <h4 className='text-lg font-medium'>₹200.25</h4>
         <p className='text-sm text-gray-600'>10.5 KM</p>
         </div>
        </div>
        <div className='flex items-center gap-2 flex-col justify-between mt-3' >
           
            <div className=' flex flex-col justify-between w-full'>
                <div className='flex border-green-500 p-3 w-full'>
                    <h5 className='pt-4'><i className="ri-map-pin-range-fill"></i></h5>
                   <div className='flex flex-col ml-6'>
                   <h3 className='font-semibold text-lg' >Pick Up</h3>
                   <p className='font-normal text-sm text-gray-500 '>Pushp Vihar, Bangalore, India</p>
                   </div>
                </div><hr className=''></hr>
                <div className='flex mt-2 border-green-500 p-3 w-full'>
                <h5 className='pt-4'><i className="ri-checkbox-blank-fill"></i></h5>
                   <div className='flex flex-col ml-6'>
                   <h3 className='font-semibold text-lg' >Drop Off</h3>
                   <p className='font-normal text-sm text-gray-500 '>17th Cross Road PWD Quartres 1<sup>st</sup> Sector Bangalore, India</p>
                   </div>
                </div><hr></hr>
            
            </div>
            <button onClick={()=>{
            props.setConfirmRidePopuppanel(true)
            }} className='bg-green-600  text-white w-full py-2 rounded-lg mt-3'>Go To Pick Up</button>
            <button onClick={()=>{
           props.setRidePopuppanel(false)
            }} className='bg-red-500  text-white w-full py-2 rounded-lg'>Ignore Ride</button>
        </div>
    </div>
  )
}

export default RidePopup
