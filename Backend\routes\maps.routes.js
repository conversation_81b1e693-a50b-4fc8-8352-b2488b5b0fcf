const express=require('express');
const router=express.Router();
const authMiddleware=require('../middleware/auth.middleware');
const mapController=require('../controllers/map.controller');
const {query} = require('express-validator');

// Middleware to log all map requests
router.use((req, _res, next) => {
    console.log('🗺️ [MAPS ROUTES] Incoming request:', {
        method: req.method,
        path: req.path,
        query: req.query,
        timestamp: new Date().toISOString()
    });
    next();
});

router.get('/get-coordinates',
    // Enhanced validation
    query('address')
        .isString()
        .withMessage('Address must be a string')
        .isLength({min:3, max:200})
        .withMessage('Address must be between 3 and 200 characters')
        .trim()
        .escape(),
    authMiddleware.authUser,
    mapController.getCoordinates
);

// Health check endpoint for maps service
router.get('/health', (_req, res) => {
    console.log('🏥 [MAPS ROUTES] Health check requested');
    res.status(200).json({
        success: true,
        message: 'Maps service is healthy',
        timestamp: new Date().toISOString(),
        service: 'maps'
    });
});

module.exports=router;