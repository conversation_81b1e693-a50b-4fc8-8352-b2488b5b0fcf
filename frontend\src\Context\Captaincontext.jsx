import React from "react";
import { createContext,useState,useContext } from "react";
import { useNavigate } from "react-router-dom";
import axios from "axios";

export const CaptainDataContext = createContext();

// export const useCaptain= () =>{
//    const context =  useContext(CaptainContext);

//    if(!context){
//     throw new Error('useCaptain must be used within a CaptainProvider');
//    }
//    return context;
// };

const Captaincontext = ({ children }) => {

    const [captain,setCaptain] = useState(null);
    const [isLoading,setIsLoading] = useState(false);
    const [error,setError] = useState(null);

    const updateCaptain = (newCaptain) =>{
        setCaptain(newCaptain);
    }

    const value ={
        captain,
        setCaptain,
        isLoading,
        setIsLoading,
        error,
        setError,
        updateCaptain
    }

  return (
    <div>
      <CaptainDataContext.Provider value={value}>
        {children}
      </CaptainDataContext.Provider>
    </div>
  )
}

export default Captaincontext
