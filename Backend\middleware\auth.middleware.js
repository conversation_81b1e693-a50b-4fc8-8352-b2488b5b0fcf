const userModel=require('../models/user.model');
const bcrypt=require('bcrypt');
const jwt=require('jsonwebtoken');
const BlacklistTokenModel = require('../models/blacklistToken.model');
const captainModel = require('../models/captain.model');



module.exports.authUser = async (req,res,next) =>{
    let token;

    // Check for token in cookies
    if (req.cookies && req.cookies.token) {
        token = req.cookies.token;
    }
    // Check for token in Authorization header
    else if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
        token = req.headers.authorization.split(' ')[1];
    }

    if(!token){
        return res.status(401).json({message:'Unauthorized - No token provided'});
    }

    const isBlacklisted = await BlacklistTokenModel.findOne({ token : token });

    if (isBlacklisted) {
        return res.status(401).json({message:'Token has been revoked'});
    }

    try{
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const user = await userModel.findById(decoded._id);

        if (!user) {
            return res.status(401).json({message:'User not found - Token may be for a deleted user'});
        }

        req.user = user;
        return next();
    }catch(err){
        if (err.name === 'JsonWebTokenError') {
            return res.status(401).json({message:'Invalid token'});
        } else if (err.name === 'TokenExpiredError') {
            return res.status(401).json({message:'Token expired'});
        } else {
            console.error('Auth error:', err);
            return res.status(500).json({message:'Server error during authentication'});
        }
    }

}

module.exports.authCaptain= async (req,res,next) =>{

    const token = req.cookies.token  || req.headers.authorization?.split(' ')[1];

    if(!token){
        return res.status(401).json({message:'Unauthorized - No token provided'});
    }

    const isBlacklisted = await BlacklistTokenModel.findOne({ token : token });

    if (isBlacklisted) {
        return res.status(401).json({message:'Token has been revoked'});
    }

    try{
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const captain = await captainModel.findById(decoded._id);

        if (!captain) {
            return res.status(401).json({message:'User not found - Token may be for a deleted user'});
        }

        req.captain = captain;
        return next();
    }catch(err){
        if (err.name === 'JsonWebTokenError') {
            return res.status(401).json({message:'Invalid token'});
        } else if (err.name === 'TokenExpiredError') {
            return res.status(401).json({message:'Token expired'});
        } else {
            console.error('Auth error:', err);
            return res.status(500).json({message:'Server error during authentication'});
        }
    }

}