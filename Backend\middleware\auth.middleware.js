const userModel=require('../models/user.model');
const bcrypt=require('bcrypt');
const jwt=require('jsonwebtoken');
const BlacklistTokenModel = require('../models/blacklistToken.model');
const captainModel = require('../models/captain.model');



module.exports.authUser = async (req,res,next) =>{
    console.log('🔐 [AUTH MIDDLEWARE] Starting user authentication');
    console.log('🍪 [AUTH MIDDLEWARE] Cookies:', req.cookies);
    console.log('📋 [AUTH MIDDLEWARE] Authorization header:', req.headers.authorization);

    let token;

    // Check for token in cookies
    if (req.cookies && req.cookies.token) {
        token = req.cookies.token;
        console.log('✅ [AUTH MIDDLEWARE] Token found in cookies');
    }
    // Check for token in Authorization header
    else if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
        token = req.headers.authorization.split(' ')[1];
        console.log('✅ [AUTH MIDDLEWARE] Token found in Authorization header');
    }

    console.log('🎫 [AUTH MIDDLEWARE] Extracted token:', token ? `${token.substring(0, 20)}...` : 'null');

    if(!token){
        console.error('❌ [AUTH MIDDLEWARE] No token found in cookies or Authorization header');
        return res.status(401).json({message:'Unauthorized - No token provided'});
    }

    console.log('🔍 [AUTH MIDDLEWARE] Checking if token is blacklisted...');
    const isBlacklisted = await BlacklistTokenModel.findOne({ token : token });

    if (isBlacklisted) {
        console.error('❌ [AUTH MIDDLEWARE] Token is blacklisted');
        return res.status(401).json({message:'Token has been revoked'});
    }
    console.log('✅ [AUTH MIDDLEWARE] Token is not blacklisted');

    try{
        console.log('🔓 [AUTH MIDDLEWARE] Verifying JWT token...');
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        console.log('✅ [AUTH MIDDLEWARE] Token verified, user ID:', decoded._id);

        console.log('👤 [AUTH MIDDLEWARE] Finding user in database...');
        const user = await userModel.findById(decoded._id);

        if (!user) {
            console.error('❌ [AUTH MIDDLEWARE] User not found in database');
            return res.status(401).json({message:'User not found - Token may be for a deleted user'});
        }

        console.log('✅ [AUTH MIDDLEWARE] User found:', user.email);
        req.user = user;
        console.log('🎉 [AUTH MIDDLEWARE] Authentication successful, proceeding to next middleware');
        return next();
    }catch(err){
        console.error('💥 [AUTH MIDDLEWARE] JWT verification failed:', err.message);
        if (err.name === 'JsonWebTokenError') {
            return res.status(401).json({message:'Invalid token'});
        } else if (err.name === 'TokenExpiredError') {
            return res.status(401).json({message:'Token expired'});
        } else {
            console.error('Auth error:', err);
            return res.status(500).json({message:'Server error during authentication'});
        }
    }

}

module.exports.authCaptain= async (req,res,next) =>{

    const token = req.cookies.token  || req.headers.authorization?.split(' ')[1];

    if(!token){
        return res.status(401).json({message:'Unauthorized - No token provided'});
    }

    const isBlacklisted = await BlacklistTokenModel.findOne({ token : token });

    if (isBlacklisted) {
        return res.status(401).json({message:'Token has been revoked'});
    }

    try{
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        const captain = await captainModel.findById(decoded._id);

        if (!captain) {
            return res.status(401).json({message:'User not found - Token may be for a deleted user'});
        }

        req.captain = captain;
        return next();
    }catch(err){
        if (err.name === 'JsonWebTokenError') {
            return res.status(401).json({message:'Invalid token'});
        } else if (err.name === 'TokenExpiredError') {
            return res.status(401).json({message:'Token expired'});
        } else {
            console.error('Auth error:', err);
            return res.status(500).json({message:'Server error during authentication'});
        }
    }

}