import React, { useState, useEffect, useContext } from 'react'
import { Link , useNavigate } from 'react-router-dom'
import { UserDataContext } from '../Context/Usercontext'
import axios from 'axios'

const Userlogin = () => {
  console.log('Userlogin component rendering');

  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  // Using setData but not using data directly - keeping for future use
  const [, setData] = useState({})
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  // Only using setUser from context
  const { setUser } = useContext(UserDataContext)
  const navigate = useNavigate()

  // Check if user is already logged in
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      console.log('User already logged in, redirecting to home');
      navigate('/home');
    }
  }, [navigate]);

    const Handler= async (e)=>{
      e.preventDefault();
      setLoading(true);
      setError('');
      try {
        const newData = {
          email,
          password,
        };

        console.log('Sending login data:', newData);
        const baseUrl = import.meta.env.VITE_BASE_URL || 'http://localhost:4000';
        const response = await axios.post(`${baseUrl}/users/login`, newData);
        console.log('Login response:', response);

        if(response.status >= 200 && response.status < 300){
          const responseData = response.data;
          console.log('Login successful:', responseData);
          setData(newData); // Update local state
          const userData = responseData.user || responseData.captain || responseData;
          setUser(userData);

          // Store token in localStorage
          localStorage.setItem('token', responseData.token);

          // Also store user data in localStorage for persistence
          localStorage.setItem('userData', JSON.stringify(userData));

          console.log('Token and user data stored in localStorage');

          // Navigate to home page
          navigate('/home');
        }
      } catch (error) {
        console.error('Login error:', error);
        if (error.response) {
          console.error('Error response data:', error.response.data);
          console.error('Error response status:', error.response.status);
          setError(error.response.data.message || 'Login failed. Please check your credentials.');
        } else if (error.request) {
          console.error('Error request:', error.request);
          setError('Network error. Please check your connection.');
        } else {
          console.error('Error message:', error.message);
          setError('An unexpected error occurred.');
        }
      } finally {
        setLoading(false);
        setEmail('');
        setPassword('');
      }
    }



  return (
    <div className='p-7 flex flex-col justify-between h-screen'>
      <div>
      <img className='w-1/4 mb-5' src="https://download.logo.wine/logo/Uber/Uber-Logo.wine.png" alt="" />
        <form onSubmit={(e)=>{Handler(e)}}>
          <h3 className='text-lg'>What's your email?</h3>
          <input
          type='email'
          required
          onChange={(e) => setEmail(e.target.value)}
          value={email}
          placeholder='<EMAIL>'
          className='border border-gray-300 bg-[#eeeeee] w-full py-2 px-4 rounded mt-2' />
          <h3 className='mt-7 text-lg'>Password</h3>
          <input
          type='password'
          required
          onChange={(e)=>setPassword(e.target.value)}
          value={password}
          placeholder='**********'
          className='border border-gray-300 bg-[#eeeeee] w-full py-2 px-4 rounded mt-2' />
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mt-4 mb-4">
              {error}
            </div>
          )}
          <button
            className='flex items-center justify-center w-full bg-black text-white py-3 mt-6 rounded text-lg'
            disabled={loading}
          >
            {loading ? 'Logging in...' : 'Login'}
          </button>
        </form>
        <div className='mt-5'>
          <p className='text-center'>Don't have an account? <Link to={'/signup'} className='text-blue-500'>Sign up</Link></p>
        </div>
      </div>
      <Link to={'/Captain-login'} className='flex items-center justify-center w-full bg-[#B2EC5D] text-white py-3 mt-12 rounded text-lg mb-5 '>Sign In as Captain</Link>
    </div>
  )
}

export default Userlogin
