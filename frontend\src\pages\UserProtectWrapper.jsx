import React, { useContext, useEffect, useState } from "react";
import { UserDataContext } from "../Context/Usercontext";
import { useNavigate } from "react-router-dom";
import axios from "axios";

const UserProtectWrapper = ({children}) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const navigate = useNavigate();
  const { setUser } = useContext(UserDataContext);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Get token from localStorage
        const token = localStorage.getItem('token');
        console.log('User token from localStorage:', token);

        if (!token) {
          console.log('No user token found, redirecting to login');
          setIsAuthenticated(false);
          navigate('/login');
          return;
        }

        // Try to get user data from localStorage if available
        const userData = localStorage.getItem('userData');
        if (userData) {
          setUser(JSON.parse(userData));
        }

        // Validate token with backend
        const baseUrl = import.meta.env.VITE_BASE_URL || 'http://localhost:4000';
        const response = await axios.get(`${baseUrl}/users/profile`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        if (response.status === 200) {
          console.log('User profile fetched successfully:', response.data);
          setUser(response.data);
          setIsAuthenticated(true);
        }
      } catch (error) {
        console.error('User authentication error:', error);
        localStorage.removeItem('token');
        localStorage.removeItem('userData');
        setIsAuthenticated(false);
        navigate('/login');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [navigate, setUser]);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <>
      {isAuthenticated ? children : null}
    </>
  );
}

export default UserProtectWrapper
