import React from 'react';
import 'remixicon/fonts/remixicon.css'

const LocationSearchPanel = (props) => {

  const locations=[
    "Pnya2ndstghgnhlimnrdblr-91, Hgnhalli, Bangalore, India",
    "112 Q, Pushp Vihar, Bangalore, India",
    "4, <PERSON> Jwel Bldg, 7 Bunglows J P Rd, Opp Oriental Bank Of Commerce, Andheri (west), India",
    "10/a Glamour Bldg, Aurther Bunder Road, Colaba Mumbai, India",
    "Pnya2ndstghgnhlimnrdblr-91, Hgnhalli, Bangalore, India",
  ]

  return (
    <div >
      {locations.map((location,index)=>(
        <div onClick={
          ()=>{
             props.setPanelOpen(false)
            props.setVehiclePanel(true)
          }
        } key={index} className='flex gap-2 my-5  border-2 active:border-black border-grey-200 rounded-xl my-4 items-center justify-start'>
         <h2 className='h-7 w-7 pl-1'> <i className="ri-map-pin-line"></i></h2>
          <h4 className=''>{location}</h4>
        </div>
      ))}
      
    </div>
  );
};

export default LocationSearchPanel;
