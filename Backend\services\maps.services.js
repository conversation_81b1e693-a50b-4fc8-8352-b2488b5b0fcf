const axios=require('axios');

module.exports.getAddressCoordinates=async(address)=>{
    console.log('🔍 [MAPS SERVICE] Starting coordinate lookup for address:', address);

    // Checkpoint 1: Validate API key
    const apiKey=process.env.GOOGLE_MAPS_API;
    if (!apiKey) {
        console.error('❌ [MAPS SERVICE] Google Maps API key is not configured');
        throw new Error('Google Maps API key is not configured');
    }
    console.log('✅ [MAPS SERVICE] API key found');

    // Checkpoint 2: Validate address input
    if (!address || address.trim().length === 0) {
        console.error('❌ [MAPS SERVICE] Invalid address provided:', address);
        throw new Error('Address is required and cannot be empty');
    }
    console.log('✅ [MAPS SERVICE] Address validation passed');

    const url=`https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(address)}&key=${apiKey}`;
    console.log('🌐 [MAPS SERVICE] Making request to Google Maps API');
    console.log('🔗 [MAPS SERVICE] Request URL (without API key):', url.replace(apiKey, 'HIDDEN_API_KEY'));

    try{
        // Checkpoint 3: Make API request
        const response=await axios.get(url);
        console.log('📡 [MAPS SERVICE] API response status:', response.data.status);
        console.log('📊 [MAPS SERVICE] API response results count:', response.data.results?.length || 0);

        // Log the full response for debugging API key issues
        if (response.data.status === 'REQUEST_DENIED') {
            console.error('🚫 [MAPS SERVICE] Full API response:', JSON.stringify(response.data, null, 2));
        }

        // Checkpoint 4: Check API response status
        if(response.data.status==='OK' && response.data.results && response.data.results.length > 0){
            const location = response.data.results[0].geometry.location;
            const coordinates = {
                lat: location.lat,
                lng: location.lng  // Fixed: was 'ltd' instead of 'lng'
            };
            console.log('✅ [MAPS SERVICE] Coordinates found:', coordinates);
            return coordinates;
        }
        else if(response.data.status === 'ZERO_RESULTS'){
            console.error('❌ [MAPS SERVICE] No results found for address:', address);
            throw new Error('No coordinates found for the provided address');
        }
        else if(response.data.status === 'OVER_QUERY_LIMIT'){
            console.error('❌ [MAPS SERVICE] API quota exceeded');
            throw new Error('API quota exceeded. Please try again later');
        }
        else if(response.data.status === 'REQUEST_DENIED'){
            console.error('❌ [MAPS SERVICE] API request denied - Check API key and billing');
            console.error('🚫 [MAPS SERVICE] Full API response:', JSON.stringify(response.data, null, 2));

            // Temporary fallback for testing - return mock coordinates
            console.log('🔄 [MAPS SERVICE] Using mock coordinates for testing');
            return {
                lat: 17.3850,
                lng: 78.4867,
                mock: true,
                message: 'Mock coordinates for Hyderabad, Telangana (API key issue)'
            };
        }
        else{
            console.error('❌ [MAPS SERVICE] API error status:', response.data.status);
            throw new Error(`Failed to fetch coordinates: ${response.data.status}`);
        }
    }
    catch(error){
        console.error('💥 [MAPS SERVICE] Error occurred:', error.message);
        if (error.response) {
            console.error('📄 [MAPS SERVICE] API Error Response:', error.response.data);
        }
        throw error;
    }

}