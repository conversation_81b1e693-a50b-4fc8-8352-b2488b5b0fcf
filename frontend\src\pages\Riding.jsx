import React from 'react'
import { Link } from 'react-router-dom'
import 'remixicon/fonts/remixicon.css'

const Riding = () => {
  return (
    <div className='h-screen'>
      <Link to={'/home'} className='h-10 w-10 fixed rounded-full bg-gray-100 flex items-centre justify-center right-3 top-3 ' >
      <i className="text-xl font-bold flex items-centre justify-center ri-home-line"></i>
      </Link>
      <div className='h-1/2'>
        <img className='h-full w-full object-cover' src="https://storage.googleapis.com/support-forums-api/attachment/thread-190038693-17286932122251387759.png" alt="" />
      </div>
      <div className='h-1/2 p-4 '>
      <div className='flex justify-between items-center'>
         <img  className='h-14 pr-2 pl-5 pt-5' src="https://logodix.com/logo/640751.png" alt="" />
         <div className=' text-right'>
          <h2 className='text-lg font-medium'><PERSON></h2>
          <h3 className='text-xl font-bold -mt-2 -mb-1'>TG02A4823</h3>
          <p className='text-sm font-medium text-gray-600'>Swift Desire 2018</p>
         </div>
      </div>
     <div className='flex items-center gap-2 flex-col justify-between mt-3' >
      
         <div className=' flex flex-col justify-between w-full'>
             <hr className=''></hr>
             <div className='flex  border-green-500 p-3 w-full'>
             <h5 className='pt-4'><i className="ri-checkbox-blank-fill"></i></h5>
                <div className='flex flex-col ml-6'>
                <h1 className='font-semibold text-xl' >Gandhi Bhavan</h1>
                <p className='font-normal text-sm text-gray-500 '>17th Cross Road PWD Quartres 1<sup>st</sup> Sector Bangalore, India</p>
                </div>
             </div><hr></hr>
             <div className='flex  border-green-500 p-3 w-full'>
             <h5 className='pt-2'><i className="ri-wallet-fill"></i></h5>
                <div className='flex flex-col ml-6'>
                <h1 className='font-semibold text-xl' >$192.20</h1>
              
                </div>
             </div>
         </div>
        
     </div>
     <button  className='bg-green-600  text-white w-full py-2 rounded-lg mt-3'>Make Payment </button>
      </div>
    </div>
  )
}

export default Riding
