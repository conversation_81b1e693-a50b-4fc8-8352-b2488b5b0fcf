import React, { useContext } from 'react'
import { <PERSON> } from 'react-router-dom'
import { CaptainDataContext } from '../Context/Captaincontext'

const CaptainDashboard = () => {
  const { captain } = useContext(CaptainDataContext);
  
  return (
    <div className="p-6">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-2xl font-bold">Captain Dashboard</h1>
        <Link to="/captain/logout" className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
          Logout
        </Link>
      </div>
      
      <div className="bg-gray-100 p-4 rounded mb-6">
        <h2 className="text-xl font-semibold mb-2">Welcome, {captain?.fullname?.firstname || 'Captain'}</h2>
        <p>You are now logged in to your captain account.</p>
        <p className="mt-2">This is your dashboard where you can manage your rides and account settings.</p>
      </div>
      
      <div className="bg-white shadow rounded p-4 mb-6">
        <h3 className="text-lg font-semibold mb-2">Your Vehicle</h3>
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-gray-600">Type</p>
            <p className="font-medium">{captain?.vehicle?.vehicleType || 'Not specified'}</p>
          </div>
          <div>
            <p className="text-gray-600">Color</p>
            <p className="font-medium">{captain?.vehicle?.color || 'Not specified'}</p>
          </div>
          <div>
            <p className="text-gray-600">Plate</p>
            <p className="font-medium">{captain?.vehicle?.plate || 'Not specified'}</p>
          </div>
          <div>
            <p className="text-gray-600">Capacity</p>
            <p className="font-medium">{captain?.vehicle?.capacity || 'Not specified'}</p>
          </div>
        </div>
      </div>
      
      <div className="bg-white shadow rounded p-4">
        <h3 className="text-lg font-semibold mb-2">Account Status</h3>
        <div className="flex items-center">
          <div className={`w-3 h-3 rounded-full mr-2 ${captain?.status === 'active' ? 'bg-green-500' : 'bg-red-500'}`}></div>
          <p>{captain?.status === 'active' ? 'Active' : 'Inactive'}</p>
        </div>
      </div>
    </div>
  )
}

export default CaptainDashboard
