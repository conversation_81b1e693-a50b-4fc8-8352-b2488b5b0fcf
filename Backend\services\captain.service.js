const captainModel = require('../models/captain.model');

module.exports.createCaptain = async ({
     firstname, lastname, password, email, vechileColor,
    vechilePlate, vechileCapacity, vechileType,
}) => {

    if(!firstname||!lastname||!password||!email||!vechileColor||!vechilePlate||!vechileCapacity||!vechileType){
        throw new Error('All fields are required');
    }

    const captain = await captainModel.create({
        fullname:{
            firstname,
            lastname
        },
        email,
        password,
        vehicle:{
            color: vechileColor,
            plate: vechilePlate,
            capacity: vechileCapacity,
            vehicleType: vechileType
        }
    })

    return captain;


}