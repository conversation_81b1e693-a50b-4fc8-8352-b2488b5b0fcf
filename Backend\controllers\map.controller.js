const mapService = require('../services/maps.services');
const { validationResult } = require('express-validator');

module.exports.getCoordinates = async (req, res, next) => {
    console.log('🎯 [MAP CONTROLLER] Starting getCoordinates request');
    console.log('📝 [MAP CONTROLLER] Request query:', req.query);
    console.log('👤 [MAP CONTROLLER] User ID:', req.user?._id);

    // Checkpoint 1: Validate request input
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        console.error('❌ [MAP CONTROLLER] Validation errors:', errors.array());
        return res.status(400).json({
            success: false,
            message: 'Validation failed',
            errors: errors.array()
        });
    }
    console.log('✅ [MAP CONTROLLER] Input validation passed');

    const { address } = req.query;

    // Checkpoint 2: Additional address validation
    if (!address || typeof address !== 'string' || address.trim().length === 0) {
        console.error('❌ [MAP CONTROLLER] Invalid address parameter:', address);
        return res.status(400).json({
            success: false,
            message: 'Address parameter is required and must be a non-empty string'
        });
    }
    console.log('✅ [MAP CONTROLLER] Address parameter validated:', address);

    try {
        // Checkpoint 3: Call map service
        console.log('🔄 [MAP CONTROLLER] Calling map service...');
        const coordinates = await mapService.getAddressCoordinates(address);

        // Checkpoint 4: Validate service response
        if (!coordinates || typeof coordinates.lat !== 'number' || typeof coordinates.lng !== 'number') {
            console.error('❌ [MAP CONTROLLER] Invalid coordinates from service:', coordinates);
            return res.status(500).json({
                success: false,
                message: 'Invalid coordinates received from service'
            });
        }

        console.log('✅ [MAP CONTROLLER] Successfully retrieved coordinates:', coordinates);
        res.status(200).json({
            success: true,
            message: 'Coordinates retrieved successfully',
            data: coordinates,
            address: address
        });

    } catch (error) {
        console.error('💥 [MAP CONTROLLER] Error in getCoordinates:', error.message);

        // Checkpoint 5: Handle different types of errors
        let statusCode = 500;
        let message = 'Internal server error';

        if (error.message.includes('API key')) {
            statusCode = 503;
            message = 'Service temporarily unavailable';
        } else if (error.message.includes('No coordinates found')) {
            statusCode = 404;
            message = 'Address not found';
        } else if (error.message.includes('quota exceeded')) {
            statusCode = 429;
            message = 'Service quota exceeded. Please try again later';
        } else if (error.message.includes('request denied')) {
            statusCode = 403;
            message = 'Service access denied';
        } else if (error.message.includes('Address is required')) {
            statusCode = 400;
            message = 'Invalid address provided';
        }

        res.status(statusCode).json({
            success: false,
            message: message,
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
}