import React, { useState, useEffect, createContext } from 'react'
import axios from 'axios'

export const UserDataContext = createContext()

const Usercontext = ({children}) => {
  // Initialize user state from localStorage if available
  const [user, setUser] = useState(() => {
    const savedUserData = localStorage.getItem('userData');
    if (savedUserData) {
      try {
        return JSON.parse(savedUserData);
      } catch (error) {
        console.error('Error parsing user data from localStorage:', error);
        return {
          email: '',
          password: '',
          fullname: {
            firstname: '',
            lastname: ''
          }
        };
      }
    } else {
      return {
        email: '',
        password: '',
        fullname: {
          firstname: '',
          lastname: ''
        }
      };
    }
  });

  // Update localStorage when user state changes
  useEffect(() => {
    if (user && (user.email || (user.fullname && (user.fullname.firstname || user.fullname.lastname)))) {
      localStorage.setItem('userData', JSON.stringify(user));
    }
  }, [user]);

  return (
    <div>
      <UserDataContext.Provider value={{user, setUser}}>
        {children}
      </UserDataContext.Provider>
    </div>
  )
}

export default Usercontext
