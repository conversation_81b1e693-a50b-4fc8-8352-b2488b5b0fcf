import React, { useEffect, useState, useContext } from 'react'
import { useNavigate } from 'react-router-dom'
import axios from 'axios'
import { UserDataContext } from '../Context/Usercontext'

const Userlogout = () => {
  const [isLoggingOut, setIsLoggingOut] = useState(true);
  const navigate = useNavigate();
  const { setUser } = useContext(UserDataContext);

  useEffect(() => {
    const performLogout = async () => {
      try {
        const token = localStorage.getItem('token');
        if (!token) {
          // If no token exists, just redirect to login
          console.log('No token found, redirecting to login');
          navigate('/login');
          return;
        }

        // Call the logout API
        const baseUrl = import.meta.env.VITE_BASE_URL || 'http://localhost:4000';
        const response = await axios.get(`${baseUrl}/users/logout`, {
          headers: {
            Authorization: `Bearer ${token}`
          }
        });

        console.log('Logout response:', response);
      } catch (error) {
        console.error('Logout error:', error);
        // Even if the API call fails, we still want to clear local storage
      } finally {
        // Clear local storage and context
        localStorage.removeItem('token');
        localStorage.removeItem('userData');
        setUser({
          email: '',
          password: '',
          fullname: {
            firstname: '',
            lastname: ''
          }
        });

        // Redirect to login page
        navigate('/login');
      }
    };

    performLogout();
  }, [navigate, setUser]);

  return (
    <div className="flex items-center justify-center h-screen">
      {isLoggingOut && (
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">Logging out...</h2>
          <p>Please wait while we log you out.</p>
        </div>
      )}
    </div>
  )
}

export default Userlogout
