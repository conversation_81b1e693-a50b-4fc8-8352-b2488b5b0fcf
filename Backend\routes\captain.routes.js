const express=require('express')
const router=express.Router()
const {body}=require('express-validator')
const captainController=require('../controllers/captain.controller')
const {authCaptain} = require('../middleware/auth.middleware')


router.post('/register',[
    body('email').isEmail().withMessage('Invalid email'),
    body('password').isLength({min:6}).withMessage('Password must be at least 6 characters'),
    body(['fullname.firstname', 'firstname']).optional().isLength({min:3}).withMessage('First name must be at least 3 characters'),
    body('vehicle.color').isLength({min:3}).withMessage('Color must be at least 3 characters'),
    body('vehicle.plate').isLength({min:3}).withMessage('Plate must be at least 3 characters'),
    body('vehicle.capacity').isInt({min:1}).withMessage('Capacity must be at least 1'),
    body('vehicle.vehicleType').isIn(['car','bike','auto']).withMessage('Vehicle type must be car,bike or auto'),
],captainController.registerCaptain)

router.post('/login',[
    body('email').isEmail().withMessage('Invaild Email'),
    body('password').isLength({min:6}).withMessage('Password mustbe atleast 6 Characters'),
],
    captainController.loginCaptain)

router.get('/profile',authCaptain,captainController.getCaptainProfile)
router.get('/logout',authCaptain,captainController.logoutCaptain)

module.exports=router;