const axios = require('axios');
require('dotenv').config();

async function testGoogleMapsAPI() {
    console.log('🧪 Testing Google Maps API Key...');
    
    const apiKey = process.env.GOOGLE_MAPS_API;
    console.log('🔑 API Key:', apiKey ? `${apiKey.substring(0, 10)}...` : 'NOT FOUND');
    
    if (!apiKey) {
        console.error('❌ No API key found in environment variables');
        return;
    }
    
    // Test with a simple address
    const testAddress = 'New York, NY';
    const url = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(testAddress)}&key=${apiKey}`;
    
    console.log('🌐 Testing with address:', testAddress);
    console.log('🔗 Request URL:', url.replace(apiKey, 'HIDDEN_KEY'));
    
    try {
        const response = await axios.get(url);
        
        console.log('\n📡 API Response:');
        console.log('Status:', response.data.status);
        console.log('Results count:', response.data.results?.length || 0);
        
        if (response.data.status === 'OK') {
            console.log('✅ API Key is working!');
            console.log('📍 Coordinates:', response.data.results[0].geometry.location);
        } else {
            console.log('❌ API Error Details:');
            console.log(JSON.stringify(response.data, null, 2));
            
            // Provide specific guidance based on error
            switch (response.data.status) {
                case 'REQUEST_DENIED':
                    console.log('\n🔧 SOLUTION:');
                    console.log('1. Check if Geocoding API is enabled in Google Cloud Console');
                    console.log('2. Ensure billing is enabled on your Google Cloud project');
                    console.log('3. Check API key restrictions');
                    console.log('4. Verify the API key is correct');
                    break;
                case 'OVER_QUERY_LIMIT':
                    console.log('\n🔧 SOLUTION: You have exceeded your quota. Check your billing and limits.');
                    break;
                case 'INVALID_REQUEST':
                    console.log('\n🔧 SOLUTION: The request is invalid. Check the address format.');
                    break;
                default:
                    console.log('\n🔧 SOLUTION: Unknown error. Check Google Maps API documentation.');
            }
        }
        
    } catch (error) {
        console.error('💥 Network Error:', error.message);
        if (error.response) {
            console.error('Response data:', error.response.data);
        }
    }
}

// Run the test
testGoogleMapsAPI();
