import React, { useState, useEffect, useRef,useContext } from 'react'
import { Link } from 'react-router-dom'
import gsap from 'gsap'
import { useGSAP } from '@gsap/react'
import 'remixicon/fonts/remixicon.css'
import LocationSearchPanel from '../components/LocationSearchPanel'
import VehiclePanel from '../components/VehiclePanel'
import ConfirmRide from '../components/ConfirmRide'
import LookingForCaptain from '../components/LookingForCaptain'
import WaitingForCaptain from '../components/WaitingForCaptain'

const Index = () => {

  const [pickup, setPickup] = useState('')
  const [destination, setDestination] = useState('')
  const [panel, setPanelOpen] = useState(false)
  const [confirmRidepanel, setConfirmRidepanel] = useState(false)
  const [vehicleFoundPanel, setVehicleFoundPanel] = useState(false)
  const [waitingForCaptainPanel, setWaitingForCaptainPanel] = useState(false)
  const panelRef = useRef(null)
  const panelRefclose = useRef(null)
const [vehiclePanel,setVehiclePanel] = useState(false)
const vehiclePanelRef= useRef(null)
const confirmRidePanelRef=useRef(null)
const vehicleFoundPanelRef=useRef(null)
const waitingForCaptainPanelRef=useRef(null)

  const submitHandler = (e) => {
    e.preventDefault()
  }

  useGSAP(function () {
    if (panel) {
      gsap.to(panelRef.current, {
        height: '70%', // 70% is the height of the panel when it is open. You can adjust this value as needed.
        paddingLeft: '20px',
      })
      gsap.to(panelRefclose.current, {
        opacity: 1,
      })
    }
    else {
        gsap.to(panelRef.current, {
          height: '0%',
          paddingLeft:'20px'
        })
        gsap.to(panelRefclose.current, {
          opacity: 0,
        })
      }
  },[panel])

  useGSAP(function(){
    if(vehiclePanel){
      gsap.to(vehiclePanelRef.current,{
        translateY:'0%'
      })
    }
    else{
      gsap.to(vehiclePanelRef.current,{
        translateY:'100%'
      })
    }
  },[vehiclePanel])
  useGSAP(function(){
    if(confirmRidepanel){
      gsap.to(confirmRidePanelRef.current,{
        translateY:'0%'
      })
    }
    else{
      gsap.to(confirmRidePanelRef.current,{
        translateY:'100%'
      })
    }
  },[confirmRidepanel])
  useGSAP(function(){
    if(vehicleFoundPanel){
      gsap.to(vehicleFoundPanelRef.current,{
        translateY:'0%'
      })
    }
    else{
      gsap.to(vehicleFoundPanelRef.current,{
        translateY:'100%'
      })
    }
  },[vehicleFoundPanel])
  useGSAP(function(){
    if(waitingForCaptainPanel){
      gsap.to(waitingForCaptainPanelRef.current,{
        translateY:'0%'
      })
    }
    else{
      gsap.to(waitingForCaptainPanelRef.current,{
        translateY:'100%'
      })
    }
  },[waitingForCaptainPanel])

  return (
    <div className='h-screen relative overflow-hidden'>
      <img className='w-16 left-6 top-7 absolute' src='https://1000logos.net/wp-content/uploads/2021/04/Uber-logo.png' alt='' />
      <div  className='h-screen w-screen'>
        <img className='h-screen w-screen object-cover' src="https://storage.googleapis.com/support-forums-api/attachment/thread-190038693-17286932122251387759.png" alt="" />
      </div>
      <div  className='flex flex-col justify-end top-0 h-screen absolute w-full'>
        <div className='h-[30%]  p-5 bg-white relative'>
          <h5 onClick={() => {
            setPanelOpen(false)
          }} ref={panelRefclose} className='text-2xl opacity-0 absolute right-5 top-5 cusor-pointer'>
          <i className="ri-arrow-down-s-line"></i>
          </h5>
          <h4 className='text-xl font-base mb-3'>Find a Trip</h4>
          <form onSubmit={(e) => {
            submitHandler(e)
          }}>
            <div className="line bg-black h-16 w-0.5 absolute top-[47%] left-8 rounded-full"></div>
            <input
              onClick={() => {
                setPanelOpen(true)
              }}
              value={pickup}
              onChange={(e) => {
                setPickup(e.target.value)
              }}
              className='border border-gray-300 bg-[#eeeeee] w-full py-2 px-12 rounded-lg mt-3'
              type='text'
              placeholder='Where From?' />
            <input
              onClick={() => {
                setPanelOpen(true)
              }}
              className='border border-gray-300 bg-[#eeeeee] w-full py-2 px-12 rounded-lg mt-5'
              value={destination}
              onChange={(e) => {
                setDestination(e.target.value)
              }}
              type='text'
              placeholder='Enter your destination?' />
          </form>
        </div>

        <div ref={panelRef} className='bg-white text-centre h-0'>
                <LocationSearchPanel setPanelOpen={setPanelOpen}   setVehiclePanel={setVehiclePanel} />
        </div>
      </div>
      <div ref={vehiclePanelRef} className='bottom-0 translate-y-full  fixed z-10 bg-white w-full  px-3 py-6 pt-12'>
        <VehiclePanel setConfirmRidepanel={setConfirmRidepanel} setVehiclePanel={setVehiclePanel} setPanelOpen={setPanelOpen}/>
      </div>
      <div ref={confirmRidePanelRef} className='bottom-0 translate-y-full  fixed z-10 bg-white w-full  px-3 py-6 pt-12'>
        <ConfirmRide setConfirmRidepanel={setConfirmRidepanel} setVehicleFoundPanel={setVehicleFoundPanel}/>
      </div>
      <div ref={vehicleFoundPanelRef} className='bottom-0 translate-y-full  fixed z-10 bg-white w-full  px-3 py-6 pt-12'>
        <LookingForCaptain setVehicleFoundPanel={setVehicleFoundPanel}/>
      </div>
      <div ref={waitingForCaptainPanelRef} className='bottom-0 translate-y-full  fixed z-10 bg-white w-full  px-3 py-6 pt-12'>
        <WaitingForCaptain setWaitingForCaptainPanel={setWaitingForCaptainPanel}/>
      </div>
    </div>
  )
}

export default Index
