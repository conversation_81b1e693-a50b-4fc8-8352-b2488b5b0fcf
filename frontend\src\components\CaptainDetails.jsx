import React, { useContext } from 'react'
import { CaptainDataContext } from '../Context/Captaincontext'
import 'remixicon/fonts/remixicon.css'

const CaptainDetails = () => {
  const { captain } = useContext(CaptainDataContext);

  return (
    <div className='h-2/5 rounded-lg p-2'>
        <div className='flex justify-between h-1/2 items-center p-2'>
          <div className='' >
            <img className='h-20 pr-2 pl-5 pt-5' src="https://www.kindpng.com/picc/m/557-5575215_2020-profile-circle-hd-png-download.png" alt="" />
            <h4 className='pl-4'>{captain?.fullname?.firstname || 'Captain'} {captain?.fullname?.lastname || ''}</h4>
          </div>
          <div>
            <h5 className='text-lg'>₹200.25</h5>
            <p className='text-sm text-gray-600'>Earned</p>
          </div>
        </div>
        <div className='flex justify-between items-center bg-[#FED428] p-2 mt-3 rounded-lg'>
          <div className='text-center'>
            <i className="text-gray-600 ri-time-line"></i>
            <h4 className='text-lg font-medium'>10.2</h4>
            <p className='text-sm text-gray-600'>Hours Worked</p>
          </div>
          <div className='text-center'>
            <i className="text-gray-600 ri-speed-up-line"></i>
            <h4 className='text-lg font-medium'>30 Km</h4>
            <p className='text-sm text-gray-600'>Distance Travelled</p>
          </div>
          <div className='text-center'>
            <i className="text-gray-600 ri-bookmark-fill"></i>
            <h4 className='text-lg font-medium'>20</h4>
            <p className='text-sm text-gray-600'>Trips</p>
          </div>
        </div>
    </div>
  )
}

export default CaptainDetails
