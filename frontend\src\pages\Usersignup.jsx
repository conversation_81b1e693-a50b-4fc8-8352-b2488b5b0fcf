import React, { useState, useEffect, useContext } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import axios from 'axios'
import {UserDataContext} from '../Context/Usercontext'


const Usersignup = () => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [firstname, setFirstname] = useState('')
  const [lastname, setLastname] = useState('')
  const [data,setData] = useState({})

  const navigate = useNavigate()

  const {user,setUser} = React.useContext(UserDataContext)

  // Check if user is already logged in
  useEffect(() => {
    const token = localStorage.getItem('token');
    if (token) {
      console.log('User already logged in, redirecting to home');
      navigate('/home');
    }
  }, [navigate]);

  useEffect(() => {
    if (Object.keys(data).length > 0) {
      console.log('Data state updated:', data);
    }
  }, [data]);

  const Handler= async (e)=>{
    e.preventDefault();
    try {
      const newUser = {
        fullname:{
          firstname: firstname,
          lastname: lastname
        },
        email,
        password
      };

      // console.log('Sending data to server:', newUser);
      const baseUrl = import.meta.env.VITE_BASE_URL || 'http://localhost:4000';
      const response = await axios.post(`${baseUrl}/users/register`, newUser);

      // console.log('Server response:', response);
      if(response.status >= 200 && response.status < 300){
        const responseData = response.data;
        // console.log('Registration successful:', responseData);
        setData(newUser);
        const userData = responseData.user || responseData.captain || responseData;
        setUser(userData);

        // Store token in localStorage
        localStorage.setItem('token', responseData.token);

        // Also store user data in localStorage for persistence
        localStorage.setItem('userData', JSON.stringify(userData));

        console.log('Token and user data stored in localStorage');

        // Navigate to home page
        navigate('/home');
      }
    } catch (error) {
      console.error('Registration error:', error);
      if (error.response) {
        console.error('Error response data:', error.response.data);
        console.error('Error response status:', error.response.status);
      } else if (error.request) {
        console.error('Error request:', error.request);
      } else {
        console.error('Error message:', error.message);
      }
    } finally {
      setFirstname('');
      setLastname('');
      setEmail('');
      setPassword('');
    }
  }

  return (
    <div className='p-7 flex flex-col justify-between h-screen'>
      <div>
      <img className='w-1/4 mb-5' src="https://download.logo.wine/logo/Uber/Uber-Logo.wine.png" alt="" />
        <form onSubmit={(e)=>{Handler(e)}}>
        <h3>What's your name?</h3>
          <div className='flex justify-between gap-4 mb-6 ' >

            <input
            type='text'
            required
            onChange={(e) => setFirstname(e.target.value)}
            value={firstname}
            placeholder='First Name'
            className='border border-gray-300 bg-[#eeeeee] w-1/2 py-2 px-4 rounded mt-2'
            />

          <input
            type='text'
            required
            onChange={(e) => setLastname(e.target.value)}
            value={lastname}
            placeholder='Last Name'
            className='border border-gray-300 bg-[#eeeeee] w-1/2 py-2 px-4 rounded mt-2'
            />
          </div>

          <h3 className='text'>What's your email?</h3>
          <input
          type='email'
          required
          onChange={(e) => setEmail(e.target.value)}
          value={email}
          placeholder='<EMAIL>'
          className='border border-gray-300 bg-[#eeeeee] w-full py-2 px-4 rounded mt-2' />
          <h3 className='mt-3 text'>Password</h3>
          <input
          type='password'
          required
          onChange={(e)=>setPassword(e.target.value)}
          value={password}
          placeholder='**********'
          className='border border-gray-300 bg-[#eeeeee] w-full py-2 px-4 rounded mt-2' />
          <button className=' flex items-center justify-center w-full bg-black text-white py-3 mt-5 rounded text-lg '>Sign Up</button>
        </form>
        <div className='mt-2'>
          <p className='text-center'>Already have an account? <Link to={'/login'} className='text-blue-500'>Login</Link></p>
        </div>
      </div>
     <p className='mt-5 leading-4 text-sm'>By continuing, you agree to Uber's Terms of Service and acknowledge that you have read our Privacy Policy.
We may use your information for account security, customer support, and to send you updates about our services.</p>
    </div>
  )
}

export default Usersignup
