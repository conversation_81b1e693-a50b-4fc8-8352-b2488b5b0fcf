const userModel = require('../models/user.model');
const userService = require('../services/user.service');
const { validationResult } = require('express-validator');
const BlacklistTokenModel = require('../models/blacklistToken.model');

module.exports.registerUser = async (req, res, next) => {

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }

    const { fullname, firstname, lastname, email, password } = req.body;

    const isUserAlready = await userModel.findOne({ email });

    if (isUserAlready) {
        return res.status(400).json({ message: 'User already exist' });
    }

    const hashedPassword = await userModel.hashPassword(password);

    // Handle both nested and flat structure
    let userFirstname = firstname;
    let userLastname = lastname;

    // If fullname object is provided, use its values
    if (fullname && fullname.firstname) {
        userFirstname = fullname.firstname;
    }
    if (fullname && fullname.lastname) {
        userLastname = fullname.lastname;
    }

    const user = await userService.createUser({
        firstname: userFirstname,
        lastname: userLastname,
        email,
        password: hashedPassword
    });

    const token = user.generateAuthToken();

    res.status(201).json({ token, user });


}

module.exports.loginUser = async (req, res, next) => {

    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        return res.status(400).json({ errors: errors.array() });
    }

    const { email, password } = req.body;

    const user = await userModel.findOne({ email }).select('+password');

    if (!user) {
        return res.status(401).json({ message: 'Invalid email or password' });
    }

    const isMatch = await user.comparePassword(password);

    if (!isMatch) {
        return res.status(401).json({ message: 'Invalid email or password' });
    }

    const token = user.generateAuthToken();

    res.cookie('token', token);

    res.cookie('token', token);

    res.status(200).json({ token, user });
}

module.exports.getUserProfile = async (req, res, next) => {

    res.status(200).json(req.user);

}

module.exports.logoutUser = async (req, res, next) => {
    // Get token before clearing cookie
    const token = req.cookies.token || req.headers.authorization?.split(' ')[1];

    // Clear the cookie
    res.clearCookie('token');

    // Only blacklist if token exists
    if (token) {
        await BlacklistTokenModel.create({ token });
    }

    res.status(200).json({ message: 'Logged out' });
}