import React, { useEffect, useState, useContext } from 'react'
import { useNavigate } from 'react-router-dom'
import axios from 'axios'
import { CaptainDataContext } from '../Context/Captaincontext'

const Captainlogout = () => {
  const [isLoggingOut, setIsLoggingOut] = useState(true);
  const navigate = useNavigate();
  const { setCaptain } = useContext(CaptainDataContext);

  useEffect(() => {
    const performLogout = async () => {
      try {
        const captainToken = localStorage.getItem('captainToken');
        if (!captainToken) {
          // If no token exists, just redirect to login
          console.log('No captain token found, redirecting to captain login');
          navigate('/Captain-login');
          return;
        }

        // Call the logout API
        const baseUrl = import.meta.env.VITE_BASE_URL || 'http://localhost:4000';
        const response = await axios.get(`${baseUrl}/captains/logout`, {
          headers: {
            Authorization: `Bear<PERSON> ${captainToken}`
          }
        });

        console.log('Captain logout response:', response);
      } catch (error) {
        console.error('Captain logout error:', error);
        // Even if the API call fails, we still want to clear local storage
      } finally {
        // Clear local storage and context
        localStorage.removeItem('captainToken');
        localStorage.removeItem('captainData');
        setCaptain(null);

        // Redirect to login page
        navigate('/Captain-login');
      }
    };

    performLogout();
  }, [navigate, setCaptain]);

  return (
    <div className="flex items-center justify-center h-screen">
      {isLoggingOut && (
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">Logging out...</h2>
          <p>Please wait while we log you out.</p>
        </div>
      )}
    </div>
  )
}

export default Captainlogout
